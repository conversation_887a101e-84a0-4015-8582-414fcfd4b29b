# Science Journal Style Adaptation

## 概述

基于您的 `wlscirep_mdpi_style.cls` 文件，我创建了一个新的样式文件 `wlscirep_science_style.cls`，严格按照Science期刊的格式要求进行了修改。

## 主要修改内容

### 1. 文档类和基本设置
- **字体大小**: 从10pt改为12pt (`\LoadClass[12pt]{article}`)
- **纸张格式**: 从A4改为US Letter (`letterpaper`)
- **页边距**: 改为1英寸边距 (`margin=1in`)

### 2. 字体设置
- **主字体**: 从Palatino (mathpazo) 改为Times字体 (`newtxtext,newtxmath`)
- **行距**: 从1.13改为1.5 (双倍行距)
- **句子间距**: 添加了 `\frenchspacing` (单倍句子间距)

### 3. 图表标签格式
- **粗体标签**: 使用Science期刊特有的粗体格式
```latex
\renewcommand{\fnum@figure}{\textbf{Figure \thefigure}}
\renewcommand{\fnum@table}{\textbf{Table \thetable}}
```
- **标题字体**: 从small改为normalsize

### 4. 引用格式
- **引用包**: 从标准cite包改为Science专用的scicite包
- **参考文献标题**: 改为"References and Notes"
- **引用样式**: 使用Science期刊的引用格式

### 5. 摘要格式
- **Science风格**: 添加了Science期刊特有的摘要环境
```latex
\renewenvironment{abstract}
    {\quotation}
    {\endquotation}
```
- **粗体显示**: 摘要内容以粗体显示，无标题

### 6. 标题页格式
- **标题字体**: 调整为Science期刊风格
- **摘要处理**: 使用Science期刊的摘要格式（粗体，无标题）

### 7. 补充材料支持
- **S编号**: 添加了补充材料的S编号系统
- **切换命令**: 
  - `\supplementarymaterial` - 切换到S编号
  - `\mainmaterial` - 恢复正常编号

### 8. 其他Science期刊特色
- **日期**: 移除标题页日期显示 (`\date{}`)
- **URL支持**: 添加url包支持
- **兼容性**: 保持与原有功能的兼容性

## 保持不变的功能

以下功能按照您的要求保持不变：
- 章节标题格式和编号显示设置
- 目录格式
- 页眉页脚设置
- 作者信息块设置
- 书签和超链接设置
- 其他未明确提及的样式

## 使用方法

### 基本使用
```latex
\documentclass{wlscirep_science_style}

\title{Your Paper Title}
\author{Author Names and Affiliations}

\begin{abstract}
Your abstract text here (will appear in bold)
\end{abstract}

\keywords{keyword1; keyword2; keyword3}

\begin{document}
\maketitle

% Your content here

\end{document}
```

### 补充材料
```latex
% 在需要切换到补充材料编号时
\newpage
\supplementarymaterial

% 补充材料内容
\section*{Supplementary Materials}
% 图表将自动使用S1, S2等编号

% 如需切换回正常编号
\mainmaterial
```

## 文件列表

1. **wlscirep_science_style.cls** - 新的Science期刊样式文件
2. **science_style_example.tex** - 使用示例
3. **Science_Style_Adaptation_README.md** - 本说明文档

## 依赖包

确保您的LaTeX环境包含以下包：
- `newtxtext, newtxmath` (Times字体)
- `scicite` (Science引用格式)
- `geometry` (页面布局)
- `caption` (图表标题)
- `authblk` (作者信息)
- `fancyhdr` (页眉页脚)
- `titlesec` (章节格式)
- `hyperref` (超链接)

## 注意事项

1. 需要确保有 `scicite.sty` 文件用于Science期刊引用格式
2. 如果没有 `sciencemag.bst` 文件，会自动回退到 `unsrt` 样式
3. 建议配合Science期刊的官方模板使用以确保完全兼容
4. 保持了原有的章节编号隐藏功能和其他自定义设置

这个新样式文件严格按照Science期刊的格式要求进行了修改，同时保持了您原有样式文件的其他功能特性。
