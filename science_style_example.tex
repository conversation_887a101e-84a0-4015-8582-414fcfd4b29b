% science_style_example.tex
% Example document using the wlscirep_science_style.cls
% This demonstrates the Science journal formatting

\documentclass{wlscirep_science_style}

% Title of the paper (Science style)
\def\scititle{
	Example manuscript using Science journal formatting style
}
% Store the title in a variable for reuse
\title{\scititle}

% Author and institution list
\author{
	% You can write out first names or use initials - either way is acceptable, but be consistent
	First~Author$^{1}$,
	Second~Author$^{2}$,
	Third~Author$^{1\ast}$\and
	% Institution list, in a slightly smaller font
	\small$^{1}$Department of Example Science, University of Example, Example City, Country.\and
	\small$^{2}$Institute of Research, Research University, Research City, Country.\and
	\small$^{\ast}$Corresponding author. Email: <EMAIL>
}

% Abstract (Science style - will be bold and without heading)
\begin{abstract}
This is an example abstract demonstrating the Science journal formatting style. 
The abstract appears in bold text without a heading, following Science journal conventions.
This template adapts the original MDPI-style class to match Science journal requirements including 
Times font, 12pt text, double spacing, US letter paper with 1-inch margins, and specific 
figure/table formatting.
\end{abstract}

% Keywords
\keywords{Science journal; LaTeX template; formatting; manuscript preparation}

\begin{document}

\maketitle

\section{Introduction}

This document demonstrates the Science journal formatting style implemented in the 
\texttt{wlscirep\_science\_style.cls} class file. The style has been adapted from the 
original MDPI-style class to match Science journal requirements.

Key features of this Science-style adaptation include:
\begin{itemize}
\item Times font family (newtxtext and newtxmath packages)
\item 12pt base font size
\item Double line spacing (1.5 linespread)
\item US letter paper with 1-inch margins
\item Bold figure and table labels
\item Science-style citation formatting using scicite package
\item Abstract in bold without heading
\end{itemize}

\section{Methods}

The formatting follows Science journal guidelines as implemented in their official template.
Citations are formatted using the scicite package, which provides the proper Science 
citation style with parentheses and italic numbers~\cite{example1}.

Mathematical expressions can be included both inline like $E = mc^2$ and as displayed equations:

\begin{equation}
\frac{\partial u}{\partial t} + u \frac{\partial u}{\partial x} = \nu \frac{\partial^2 u}{\partial x^2}
\label{eq:example}
\end{equation}

\subsection{Figure and Table Formatting}

Figures and tables use bold labels as required by Science journal style. 
See Figure~\ref{fig:example} and Table~\ref{tab:example} for examples.

\section{Results}

Results would be presented here with appropriate figures and tables.
The formatting ensures compliance with Science journal requirements.

\section{Discussion}

Discussion of results follows here. The double spacing and Times font 
provide the professional appearance expected by Science journal.

\section*{Acknowledgments}

Acknowledgments section uses unnumbered section heading.

\section*{References and Notes}

% Note: In actual use, you would use \bibliography{your_bib_file} here
% For this example, we'll show the reference format

\begin{thebibliography}{1}

\bibitem{example1}
Author, A. N. Example reference for Science journal formatting. 
\textit{Journal Name} \textbf{123}, 456-789 (2024).

\end{thebibliography}

% Example of switching to supplementary material numbering
\newpage
\supplementarymaterial

\section*{Supplementary Materials for \scititle}

This section demonstrates the supplementary material formatting where 
figures, tables, and equations are numbered with S prefix.

\subsection*{Materials and Methods}

Additional methods details would go here.

\begin{figure}[h]
\centering
\rule{6cm}{4cm} % Placeholder for actual figure
\caption{\textbf{Example supplementary figure.} 
This figure demonstrates the S-numbering system for supplementary materials.}
\label{fig:sup_example}
\end{figure}

\begin{table}[h]
\centering
\caption{\textbf{Example supplementary table.} 
This table shows the S-numbering for supplementary materials.}
\label{tab:sup_example}
\begin{tabular}{lcc}
\toprule
Parameter & Value & Unit \\
\midrule
Example 1 & 1.23 & m/s \\
Example 2 & 4.56 & kg \\
\bottomrule
\end{tabular}
\end{table}

\end{document}
