% --- START OF FILE wlscirep_mdpi_style.cls ---
%
% An unofficial LaTeX class for Scientific Report articles.
% MODIFIED to approximate MDPI style for fonts, layout, captions, line numbers, and footers.
% v1.0 : Initial adaptation
% v1.1 : Title size/spacing, abstract justify, subsubsection bold, bookmarks.
% v1.2 : Title-Author space increased, subsubsection bold+italic.
% v1.3 : Fix bookmark issue by removing addcontentsline for bibliography
% v1.4 : FIX: Remove section/sub/subsub number display in headings %%% <<< MODIFIED >>> %%%
%
\NeedsTeXFormat{LaTeX2e}
%%% <<< FIX START: Updated version and name >>> %%%
\ProvidesClass{wlscirep_mdpi_style}[2024/07/30, v1.4 MDPI Style Adaptation] 
%%% <<< FIX END >>> %%%
\RequirePackage[utf8]{inputenc}
\RequirePackage[english]{babel}

\RequirePackage{ifthen}
\RequirePackage{calc}
\AtEndOfClass{\RequirePackage{microtype}} % microtype 通常是好的实践
\DeclareOption*{\PassOptionsToClass{\CurrentOption}{article}}
\ProcessOptions*
\newcommand{\JournalTitle}[1]{#1}
\LoadClass[10pt,a4paper]{article} % MDPI 通常基于 10pt a4paper article

% --- 字体设置 (MDPI 风格) ---
\RequirePackage{amsmath,amsfonts,amssymb}
\RequirePackage{mathpazo} % Palatino for text and math
\linespread{1.13} 
\RequirePackage[scaled=0.90]{helvet} % Sans-serif (Helvetica, scaled)
\RequirePackage{courier}       % Typewriter font (Courier)

\RequirePackage{ifpdf}

\RequirePackage{graphicx,xcolor}
\RequirePackage{booktabs} % MDPI 也常用 booktabs

% --- 作者信息块设置 ---
\RequirePackage{authblk}
\setlength{\affilsep}{1em} 
\renewcommand\Authfont{\normalfont\bfseries\fontsize{11}{14}\selectfont} 
\renewcommand\Affilfont{\normalfont\fontsize{9}{11}\selectfont}     

% --- 页面几何布局 (MDPI 风格) ---
\RequirePackage[left=2.5cm,%
                right=2.5cm,%
                top=2.5cm,%   
                bottom=2.5cm,% 
                headheight=14.5pt,% (fancyhdr recommendation for 10pt font)
                includefoot,%
                a4paper]{geometry}% 使用 a4paper

% --- 图表标题设置 (MDPI 风格) ---
\RequirePackage{caption}
\captionsetup[figure]{%
    labelfont={bf,small}, % 粗体, 小号
    labelsep=period,      % 标签后用句号
    textfont={small},     % 文本小号
    justification=justified, % 两端对齐
    singlelinecheck=false, 
    margin=10pt, 
    aboveskip=6pt,
    belowskip=0pt 
}
\captionsetup[table]{%
    position=top, % 表格标题在顶部
    labelfont={bf,small},
    labelsep=period,
    textfont={small},
    justification=justified,
    singlelinecheck=false,
    margin=10pt,
    aboveskip=6pt,
    belowskip=2pt % 表格标题与表格间的间距
}


% --- 参考文献设置 ---
\RequirePackage[superscript,biblabel,nomove]{cite} % wlscirep 原设置
% Check if the style file exists, otherwise use a fallback
\IfFileExists{sciencemag.bst}
 {\bibliographystyle{sciencemag}} % Science journal style
 {\bibliographystyle{unsrt}} % Fallback if style not found

\renewcommand{\@biblabel}[1]{#1.} 

% --- 页眉页脚设置 ---
\RequirePackage{fancyhdr}
\RequirePackage{lastpage}
\pagestyle{fancy}
\fancyhf{} % 清空
\lhead{} 
\chead{} 
\cfoot{\footnotesize\thepage} % 
\rfoot{} 
\renewcommand{\headrulewidth}{0pt} 
\renewcommand{\footrulewidth}{0pt} 

% --- 行号设置 (MDPI 风格) ---
 %\RequirePackage{lineno} % <-- Commented out, lineno can sometimes interfere, enable only if needed

% --- 章节标题设置 (MODIFIED) ---
\RequirePackage[explicit]{titlesec}
\definecolor{mdpiTitleColor}{rgb}{0,0,0} 
\setcounter{secnumdepth}{3} % <<< FIX 1: Keep depth=3 for \ref, TOC, and bookmarks numbering to work! >>>

%%% <<< FIX START: Remove number display from section, subsection, subsubsection titles >>> %%%
% NOTE: The numbers still exist internally for \label/\ref and will appear in TOC and Bookmarks.
% To remove numbers from TOC, modify \titlecontents commands below (e.g., remove \contentslabel).

% SECTION: 12pt, Bold, Block
\titleformat{\section}
  {\normalfont\fontsize{12}{14}\selectfont\bfseries\color{mdpiTitleColor}} 
  %{\thesection.} % Original: Numbered format
  {} % FIX: No number displayed
  %{0.5em}     % Original: Space after number
  {0em}       % FIX: No space
  {#1}      
  []
% Define the style for \section* (numberless) - used for Acks, Refs etc.
\titleformat{name=\section,numberless} 
  {\normalfont\fontsize{12}{14}\selectfont\bfseries\color{mdpiTitleColor}} 
  {} % No number
  {0em}
  {#1}
  []  
\titlespacing*{\section}{0pt}{1.2\baselineskip}{0.6\baselineskip} 

% SUBSECTION: 10pt, Bold, Block
\titleformat{\subsection}
  {\normalfont\fontsize{10}{12}\selectfont\bfseries\color{mdpiTitleColor}} 
  %{\thesubsection.} % Original
  {} % FIX: No number displayed
  %{0.5em} % Original
   {0em} % FIX: No space
  {#1}
  []
\titlespacing*{\subsection}{0pt}{1.0\baselineskip}{0.5\baselineskip} 

% SUBSUBSECTION: 10pt, BOLD+ITALIC, RUN-IN 
\titleformat{\subsubsection}[runin] 
   {\normalfont\fontsize{10}{12}\selectfont\bfseries\itshape\color{mdpiTitleColor}} % bfseries + itshape
  %{\thesubsubsection.} % Original
   {} % FIX: No number displayed
  %{0.5em} % Original: 编号与标题间距
  {0em} % FIX: No space
  {#1} % 标题文本
  [.\ ] % 标题后加点和空格，与正文隔开
\titlespacing*{\subsubsection}{0pt}{0.8\baselineskip}{0em} % 
%%% <<< FIX END >>> %%%


\titleformat{\paragraph}[runin] % MDPI paragraph 通常是 runin
  {\normalfont\normalsize\bfseries} % MDPI paragraph 标题加粗
  {} % 通常无编号
  {0em}
  {#1} 
  [.\ ] 
\titlespacing*{\paragraph}{0pt}{0.8\baselineskip}{0em} 

% tableofcontents set-up
% NOTE: These commands control TOC display. Numbers will appear here unless \contentslabel is removed/modified.
\usepackage{titletoc}
\contentsmargin{0cm}
\titlecontents{section}[\tocsep]
  {\addvspace{4pt}\normalfont\small\bfseries} 
  {\contentslabel[\thecontentslabel]{\tocsep}}
  {}
  {\hfill\normalfont\thecontentspage}
  []
\titlecontents{subsection}[\tocsep]
  {\addvspace{2pt}\normalfont\small} 
  {\contentslabel[\thecontentslabel]{\tocsep}}
  {}
  {\ \titlerule*[.5pc]{.}\ \normalfont\thecontentspage}
  []
\titlecontents*{subsubsection}[\tocsep]
  {\normalfont\footnotesize} 
  {}
  {}
  {}
  [\ \textbullet\ ]  
  
\RequirePackage{enumitem}

% article meta data
\newcommand{\keywords}[1]{\def\@keywords{#1}}

% abstract 定义 
\def\xabstract{abstract}
\long\def\abstract#1\end#2{%
    \def\two{#2}%
    \ifx\two\xabstract
        \long\gdef\theabstract{\ignorespaces#1}% 移除 \noindent
        \def\go{\end{abstract}}%
    \else
        \typeout{^^J^^J PLEASE DO NOT USE ANY \string\begin\space \string\end^^J COMMANDS WITHIN ABSTRACT^^J^^J}#1\end{#2}%
        \gdef\theabstract{\vskip12pt BADLY FORMED ABSTRACT: PLEASE DO NOT USE {\tt\string\begin...\string\end} COMMANDS WITHIN THE ABSTRACT\vskip12pt}\let\go\relax
    \fi
    \go}

% custom title page (MODIFIED: Title-Author Space)
\renewcommand{\@maketitle}{%
     \thispagestyle{fancy}% 
       
        % --- Title Block ---
         \begingroup % Local group for title style
         \raggedright % Apply left-align only to title
        {\normalfont\fontsize{20}{21}\selectfont\bfseries \@title \par} % 20pt font, 21pt leading (tight spacing)
        \endgroup
		
		% 修改前： \vspace{1.2ex} 
        \vspace{2.5ex} % Increased space between title and authors. Adjust 2.5ex if needed.
        
        % --- Author Block ---
         \begingroup % Local group for author style
         \raggedright % Apply left-align only to author block
        {\normalfont\fontsize{11}{14}\selectfont \<AUTHOR> % 作者
        \endgroup
        
         \vspace{1.5ex} % Space between authors/affiliations and abstract

        % --- Abstract Block (defaults to Justified) ---
         \ifdefined\theabstract
            \noindent 
            {\normalfont\small\bfseries Abstract:~}\begingroup\normalfont\small\theabstract\endgroup 
            \par\vspace{1ex}% 
        \fi

        % --- Keywords Block (defaults to Justified) ---
        \ifdefined\@keywords
            \noindent
             {\normalfont\small\bfseries Keywords:~}\begingroup\normalfont\small\@keywords\endgroup
            \par\vspace{1ex}% 
        \fi
        
        \vspace{0.8ex} % Space before main text starts
    \par
}
%-----------------------------------------------
\setlength{\columnsep}{0.55cm} 
\definecolor{color1}{RGB}{0,0,0} 
\newcommand{\keywordname}{Keywords:} 
\newlength{\tocsep} 
\setlength\tocsep{1.5pc} 
% FIX Bookmark: Ensure sections, subsections, subsubsections are included
\setcounter{tocdepth}{3} 

%-----------------------------------------------
% FIX BOOKMARK: Remove the \addcontentsline from here, it is now handled in main-tex.txt
% with \phantomsection\addcontentsline{toc}{section}{References} before \bibliography
\let\oldbibliography\thebibliography
\renewcommand{\thebibliography}[1]{%
%\ifthenelse{\isundefined{\contentsline}}{}{% % <<<--- FIX: COMMENT OUT
%    \addcontentsline{toc}{section}{\hspace*{-\tocsep}\refname}% % <<<--- FIX: COMMENT OUT
%}% % <<<--- FIX: COMMENT OUT
\oldbibliography{#1}%
\setlength\itemsep{0pt}% 
}

% hyperref must be loaded late. Add bookmark options. Must be AFTER titlesec
% bookmarksnumbered=true ensures numbers appear in PDF bookmarks even if not in headings
%%% <<< FIX: Remove numbers from PDF Bookmarks >>> %%%
\RequirePackage[bookmarks=true, bookmarksnumbered=false, colorlinks=true, allcolors=blue, pdfstartview=FitH]{hyperref} 

% 确保在文档开始时启用行号 (如果需要全局行号)
 % \AtBeginDocument{\linenumbers} % <-- enable only if needed

\endinput
%--- END OF FILE wlscirep_mdpi_style.cls ---